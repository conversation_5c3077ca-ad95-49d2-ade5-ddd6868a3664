import cv2
import torch
from ultralytics import YOLO
import numpy as np
import time
from datetime import datetime
import sqlite3

class SecurityDetector:
    def __init__(self, model_path='yolov5s.pt', confidence_threshold=0.5):
        """
        Initialize the security detector
        
        Args:
            model_path: Path to YOLO model file
            confidence_threshold: Minimum confidence for detections
        """
        print("Loading YOLO model...")
        self.model = YOLO(model_path)
        self.confidence_threshold = confidence_threshold
        
        # Classes we care about for security
        self.security_classes = {
            0: 'person',
            1: 'bicycle', 
            2: 'car',
            3: 'motorcycle',
            5: 'bus',
            7: 'truck',
            15: 'cat',
            16: 'dog'
        }
        
        # Alert classes (things that should trigger alerts)
        self.alert_classes = [0, 1, 2, 3, 5, 7]  # person, bicycle, car, motorcycle, bus, truck
        
        print("Security detector initialized successfully!")
    
    def detect_objects(self, frame):
        """
        Detect objects in a single frame
        
        Args:
            frame: OpenCV image frame
            
        Returns:
            tuple: (processed_frame, detections_list, alert_triggered)
        """
        # Run YOLO detection
        results = self.model(frame, conf=self.confidence_threshold)
        
        detections = []
        alert_triggered = False
        
        # Process results
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Extract box coordinates
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf[0])
                    class_id = int(box.cls[0])
                    
                    # Only process classes we care about
                    if class_id in self.security_classes:
                        class_name = self.security_classes[class_id]
                        
                        # Check if this should trigger an alert
                        if class_id in self.alert_classes:
                            alert_triggered = True
                            color = (0, 0, 255)  # Red for alerts
                            thickness = 3
                        else:
                            color = (0, 255, 0)  # Green for normal objects
                            thickness = 2
                        
                        # Draw bounding box
                        cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)
                        
                        # Draw label
                        label = f"{class_name}: {confidence:.2f}"
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                                    (x1 + label_size[0], y1), color, -1)
                        cv2.putText(frame, label, (x1, y1 - 5), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                        
                        # Store detection info
                        detections.append({
                            'class': class_name,
                            'confidence': confidence,
                            'bbox': [x1, y1, x2, y2],
                            'timestamp': datetime.now().isoformat(),
                            'alert': class_id in self.alert_classes
                        })
        
        return frame, detections, alert_triggered
    
    def process_video_stream(self, source=0, save_video=False):
        """
        Process live video stream
        
        Args:
            source: Video source (0 for webcam, or video file path)
            save_video: Whether to save processed video
        """
        # Initialize video capture
        cap = cv2.VideoCapture(source)
        
        if not cap.isOpened():
            print("Error: Could not open video source")
            return
        
        # Get video properties
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Setup video writer if saving
        if save_video:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter('security_output.mp4', fourcc, fps, (width, height))
        
        print("Starting video processing... Press 'q' to quit")
        
        frame_count = 0
        alert_cooldown = 0  # Prevent spam alerts
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Process every 3rd frame for better performance
            if frame_count % 3 == 0:
                processed_frame, detections, alert_triggered = self.detect_objects(frame)
                
                # Handle alerts
                if alert_triggered and alert_cooldown <= 0:
                    self.log_alert(detections)
                    alert_cooldown = 30  # 30 frame cooldown
                
                if alert_cooldown > 0:
                    alert_cooldown -= 1
                
                # Add status overlay
                status = "ALERT!" if alert_triggered else "MONITORING"
                color = (0, 0, 255) if alert_triggered else (0, 255, 0)
                cv2.putText(processed_frame, status, (20, 30), 
                          cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
                
                # Add timestamp
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                cv2.putText(processed_frame, timestamp, (20, height - 20), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                # Save frame if required
                if save_video:
                    out.write(processed_frame)
                
                # Display frame
                cv2.imshow('Smart Security System', processed_frame)
            
            # Check for quit command
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # Cleanup
        cap.release()
        if save_video:
            out.release()
        cv2.destroyAllWindows()
        print("Video processing stopped")
    
    def log_alert(self, detections):
        """
        Log security alerts to database
        
        Args:
            detections: List of detection dictionaries
        """
        try:
            conn = sqlite3.connect('security_logs.db')
            cursor = conn.cursor()
            
            # Create table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    object_class TEXT,
                    confidence REAL,
                    bbox TEXT,
                    alert_level TEXT
                )
            ''')
            
            # Insert alert records
            for detection in detections:
                if detection['alert']:
                    cursor.execute('''
                        INSERT INTO security_alerts 
                        (timestamp, object_class, confidence, bbox, alert_level)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        detection['timestamp'],
                        detection['class'],
                        detection['confidence'],
                        str(detection['bbox']),
                        'HIGH' if detection['class'] == 'person' else 'MEDIUM'
                    ))
            
            conn.commit()
            conn.close()
            
            print(f"Alert logged: {len([d for d in detections if d['alert']])} objects detected")
            
        except Exception as e:
            print(f"Error logging alert: {e}")

# Example usage
if __name__ == "__main__":
    # Initialize detector
    detector = SecurityDetector()
    
    # Start monitoring (use 0 for webcam, or provide video file path)
    detector.process_video_stream(source=0, save_video=True)