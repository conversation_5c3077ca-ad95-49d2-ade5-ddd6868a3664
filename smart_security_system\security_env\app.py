#!/usr/bin/env python3
"""
Smart Home Security System
Main application entry point

This application combines OpenCV and YOLO to create a comprehensive
home security monitoring system with real-time object detection.
"""

import sys
import os
import argparse
import threading
import time
from datetime import datetime
import signal

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from detector import SecurityDetector
from database import SecurityDatabase
from web_interface import app
from config import Config
import cv2

class SecuritySystem:
    def __init__(self):
        """Initialize the security system"""
        self.detector = None
        self.database = None
        self.config = Config()
        self.is_running = False
        self.web_server_thread = None
        self.monitoring_thread = None
        
        print("🔒 Smart Home Security System")
        print("=" * 50)
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n📡 Received signal {signum}, shutting down gracefully...")
        self.cleanup()
        sys.exit(0)
        
    def initialize_components(self):
        """Initialize all system components"""
        try:
            print("📋 Initializing system components...")
            
            # Initialize database
            print("  🗄️  Setting up database...")
            self.database = SecurityDatabase(self.config.DATABASE_PATH)
            self.database.log_system_event('INFO', 'System initialization started')
            
            # Initialize detector
            print("  🎯 Loading YOLO model...")
            self.detector = SecurityDetector(
                model_path=self.config.YOLO_MODEL_PATH,
                confidence_threshold=self.config.DETECTION_CONFIDENCE
            )
            
            # Test camera
            print("  📹 Testing camera connection...")
            cap = cv2.VideoCapture(self.config.CAMERA_SOURCE)
            if not cap.isOpened():
                print("  ⚠️  Warning: Could not open camera")
                return False
            
            # Set camera properties
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.CAMERA_WIDTH)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.CAMERA_HEIGHT)
            cap.set(cv2.CAP_PROP_FPS, self.config.CAMERA_FPS)
            cap.release()
            
            print("  ✅ All components initialized successfully!")
            return True
            
        except Exception as e:
            print(f"  ❌ Error initializing components: {e}")
            if self.database:
                self.database.log_system_event('ERROR', f'System initialization failed: {e}')
            return False
    
    def start_web_interface(self, host=None, port=None):
        """Start the web interface in a separate thread"""
        host = host or self.config.WEB_HOST
        port = port or self.config.WEB_PORT
        
        def run_web_app():
            try:
                print(f"🌐 Starting web interface on http://{host}:{port}")
                app.run(host=host, port=port, debug=self.config.DEBUG, threaded=True)
            except Exception as e:
                print(f"❌ Error starting web interface: {e}")
                if self.database:
                    self.database.log_system_event('ERROR', f'Web interface error: {e}')
        
        self.web_server_thread = threading.Thread(target=run_web_app)
        self.web_server_thread.daemon = True
        self.web_server_thread.start()
        
        # Give the web server time to start
        time.sleep(2)
        print(f"✅ Web interface started successfully!")
        print(f"📱 Access dashboard at: http://localhost:{port}")
        
        if self.database:
            self.database.log_system_event('INFO', f'Web interface started on {host}:{port}')
    
    def run_console_mode(self, source=None, save_video=False):
        """Run in console mode without web interface"""
        source = source if source is not None else self.config.CAMERA_SOURCE
        print("🖥️  Starting console mode...")
        print("   Press 'q' to quit")
        
        try:
            self.detector.process_video_stream(
                source=source, 
                save_video=save_video,
                output_path=self.config.VIDEO_OUTPUT_PATH
            )
        except KeyboardInterrupt:
            print("\n⏹️  Stopping system...")
        except Exception as e:
            print(f"❌ Error in console mode: {e}")
            if self.database:
                self.database.log_system_event('ERROR', f'Console mode error: {e}')
    
    def start_monitoring_loop(self):
        """Start continuous monitoring loop in a separate thread"""
        if self.is_running:
            print("⚠️  Monitoring is already running")
            return
            
        self.is_running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        print("🔄 Monitoring loop started...")
        if self.database:
            self.database.log_system_event('INFO', 'Monitoring loop started')
    
    def stop_monitoring_loop(self):
        """Stop the monitoring loop"""
        if not self.is_running:
            print("⚠️  Monitoring is not running")
            return
            
        self.is_running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
            
        print("⏹️  Monitoring loop stopped")
        if self.database:
            self.database.log_system_event('INFO', 'Monitoring loop stopped')
    
    def _monitoring_loop(self):
        """Internal monitoring loop"""
        # Initialize camera
        cap = cv2.VideoCapture(self.config.CAMERA_SOURCE)
        if not cap.isOpened():
            print("❌ Could not open camera")
            self.is_running = False
            return
        
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.CAMERA_WIDTH)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.CAMERA_HEIGHT)
        cap.set(cv2.CAP_PROP_FPS, self.config.CAMERA_FPS)
        
        frame_count = 0
        alert_cooldown = 0
        last_cleanup = time.time()
        
        try:
            while self.is_running:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Failed to capture frame")
                    break
                
                frame_count += 1
                
                # Process every nth frame for performance
                if frame_count % self.config.FRAME_SKIP == 0:
                    processed_frame, detections, alert_triggered = self.detector.detect_objects(frame)
                    
                    # Log alerts
                    if alert_triggered and alert_cooldown <= 0:
                        for detection in detections:
                            if detection['alert']:
                                alert_id = self.database.log_alert(detection)
                                print(f"🚨 ALERT: {detection['class']} detected! (ID: {alert_id})")
                        
                        alert_cooldown = self.config.ALERT_COOLDOWN
                    
                    if alert_cooldown > 0:
                        alert_cooldown -= 1
                
                # Periodic database cleanup
                current_time = time.time()
                if current_time - last_cleanup > self.config.CLEANUP_INTERVAL:
                    self.database.cleanup_old_data(self.config.DATA_RETENTION_DAYS)
                    last_cleanup = current_time
                
                time.sleep(self.config.LOOP_DELAY)
                
        except KeyboardInterrupt:
            print("\n⏹️  Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Error in monitoring loop: {e}")
            if self.database:
                self.database.log_system_event('ERROR', f'Monitoring loop error: {e}')
        finally:
            cap.release()
            self.is_running = False
    
    def show_system_info(self):
        """Display system information"""
        print("\n📊 System Information:")
        print("-" * 50)
        print(f"🐍 Python Version: {sys.version}")
        print(f"📅 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⚙️  Configuration loaded from: {self.config.CONFIG_FILE}")
        
        # Check OpenCV installation
        try:
            import cv2
            print(f"📹 OpenCV Version: {cv2.__version__}")
        except ImportError:
            print("❌ OpenCV not installed")
        
        # Check PyTorch/Ultralytics installation
        try:
            import torch
            print(f"🔥 PyTorch Version: {torch.__version__}")
            print(f"🎯 CUDA Available: {torch.cuda.is_available()}")
        except ImportError:
            print("❌ PyTorch not installed")
        
        try:
            import ultralytics
            print(f"🎯 Ultralytics Version: {ultralytics.__version__}")
        except ImportError:
            print("❌ Ultralytics not installed")
        
        # Check database
        if self.database:
            try:
                recent_alerts = self.database.get_recent_alerts(limit=5)
                print(f"🗄️  Recent Alerts: {len(recent_alerts)}")
                
                stats = self.database.get_daily_stats(days=1)
                if stats:
                    today_stats = stats[0]
                    print(f"📈 Today's Detections: {today_stats.get('total_detections', 0)}")
                    print(f"🚨 Today's Alerts: {today_stats.get('alert_count', 0)}")
            except Exception as e:
                print(f"❌ Database error: {e}")
        
        # Configuration summary
        print("\n⚙️  Configuration Summary:")
        print(f"   Camera Source: {self.config.CAMERA_SOURCE}")
        print(f"   Camera Resolution: {self.config.CAMERA_WIDTH}x{self.config.CAMERA_HEIGHT}")
        print(f"   Detection Confidence: {self.config.DETECTION_CONFIDENCE}")
        print(f"   Web Interface: {self.config.WEB_HOST}:{self.config.WEB_PORT}")
        print(f"   Model Path: {self.config.YOLO_MODEL_PATH}")
        print(f"   Database Path: {self.config.DATABASE_PATH}")
    
    def run_health_check(self):
        """Run system health check"""
        print("\n🏥 Running System Health Check...")
        print("-" * 40)
        
        health_status = {
            'database': False,
            'detector': False,
            'camera': False,
            'model': False
        }
        
        # Check database
        try:
            if self.database:
                self.database.log_system_event('INFO', 'Health check performed')
                health_status['database'] = True
                print("✅ Database: OK")
            else:
                print("❌ Database: Not initialized")
        except Exception as e:
            print(f"❌ Database: Error - {e}")
        
        # Check detector
        try:
            if self.detector:
                health_status['detector'] = True
                print("✅ Detector: OK")
            else:
                print("❌ Detector: Not initialized")
        except Exception as e:
            print(f"❌ Detector: Error - {e}")
        
        # Check camera
        try:
            cap = cv2.VideoCapture(self.config.CAMERA_SOURCE)
            if cap.isOpened():
                health_status['camera'] = True
                print("✅ Camera: OK")
                cap.release()
            else:
                print("❌ Camera: Cannot open")
        except Exception as e:
            print(f"❌ Camera: Error - {e}")
        
        # Check model file
        try:
            if os.path.exists(self.config.YOLO_MODEL_PATH):
                health_status['model'] = True
                print("✅ Model File: OK")
            else:
                print("❌ Model File: Not found")
        except Exception as e:
            print(f"❌ Model File: Error - {e}")
        
        # Overall health
        healthy_components = sum(health_status.values())
        total_components = len(health_status)
        
        print(f"\n🏥 Overall Health: {healthy_components}/{total_components} components healthy")
        
        if healthy_components == total_components:
            print("✅ System is fully operational!")
        elif healthy_components >= total_components * 0.75:
            print("⚠️  System is mostly operational with some issues")
        else:
            print("❌ System has significant issues")
        
        return health_status
    
    def cleanup(self):
        """Clean up resources"""
        print("🧹 Cleaning up resources...")
        
        # Stop monitoring
        if self.is_running:
            self.stop_monitoring_loop()
        
        # Log shutdown
        if self.database:
            self.database.log_system_event('INFO', 'System shutdown')
            
        print("✅ Cleanup completed")

def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description='Smart Home Security System')
    parser.add_argument('--mode', choices=['web', 'console', 'info', 'health', 'monitor'], 
                       default='web', help='Run mode')
    parser.add_argument('--host', default=None, 
                       help='Web interface host (overrides config)')
    parser.add_argument('--port', type=int, default=None, 
                       help='Web interface port (overrides config)')
    parser.add_argument('--source', type=int, default=None, 
                       help='Camera source (overrides config)')
    parser.add_argument('--save-video', action='store_true', 
                       help='Save processed video output')
    parser.add_argument('--config', default='config.py', 
                       help='Configuration file path')
    parser.add_argument('--no-web', action='store_true', 
                       help='Run monitoring without web interface')
    parser.add_argument('--daemon', action='store_true', 
                       help='Run as daemon process')
    
    args = parser.parse_args()
    
    # Initialize security system
    security_system = SecuritySystem()
    
    # Override config file if specified
    if args.config != 'config.py':
        security_system.config.CONFIG_FILE = args.config
        security_system.config.load_config()
    
    try:
        if args.mode == 'info':
            # Show system information
            security_system.show_system_info()
            
        elif args.mode == 'health':
            # Run health check
            if security_system.initialize_components():
                health_status = security_system.run_health_check()
                sys.exit(0 if all(health_status.values()) else 1)
            else:
                print("❌ Failed to initialize components for health check")
                sys.exit(1)
                
        elif args.mode == 'console':
            # Initialize components
            if not security_system.initialize_components():
                print("❌ Failed to initialize system components")
                sys.exit(1)
            
            # Run in console mode
            security_system.run_console_mode(
                source=args.source, 
                save_video=args.save_video
            )
            
        elif args.mode == 'monitor':
            # Initialize components
            if not security_system.initialize_components():
                print("❌ Failed to initialize system components")
                sys.exit(1)
            
            # Start monitoring loop without web interface
            security_system.start_monitoring_loop()
            
            try:
                while security_system.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n⏹️  Stopping monitoring...")
                security_system.stop_monitoring_loop()
                
        elif args.mode == 'web':
            # Initialize components
            if not security_system.initialize_components():
                print("❌ Failed to initialize system components")
                sys.exit(1)
            
            # Start web interface
            security_system.start_web_interface(
                host=args.host, 
                port=args.port
            )
            
            # Start monitoring if not disabled
            if not args.no_web:
                security_system.start_monitoring_loop()
            
            try:
                # Keep main thread alive
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n⏹️  Shutting down...")
                security_system.cleanup()
                
        else:
            print(f"❌ Unknown mode: {args.mode}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        security_system.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()