<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Security System Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .status-item {
            text-align: center;
        }

        .status-item h3 {
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin: 0 auto;
            transition: all 0.3s ease;
        }

        .status-active {
            background: #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.6);
        }

        .status-inactive {
            background: #f44336;
            box-shadow: 0 0 20px rgba(244, 67, 54, 0.6);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-start {
            background: #4CAF50;
            color: white;
        }

        .btn-start:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-stop {
            background: #f44336;
            color: white;
        }

        .btn-stop:hover {
            background: #da190b;
            transform: translateY(-2px);
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .video-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            color: white;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .video-feed {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #222;
            color: #888;
            font-size: 1.2rem;
        }

        .detection-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            color: white;
        }

        .detection-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .detection-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detection-alert {
            background: rgba(244, 67, 54, 0.3);
            border-left: 4px solid #f44336;
        }

        .alerts-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            color: white;
        }

        .alert-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #f44336;
        }

        .alert-high {
            border-left-color: #f44336;
        }

        .alert-medium {
            border-left-color: #ff9800;
        }

        .alert-low {
            border-left-color: #4caf50;
        }

        .alert-timestamp {
            font-size: 0.9rem;
            color: #ccc;
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #ccc;
        }

        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 15px;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🔒 Smart Security System</h1>
            <p>AI-Powered Home Security Monitoring</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <h3>Monitoring</h3>
                <div class="status-indicator" id="monitoring-status"></div>
            </div>
            <div class="status-item">
                <h3>Camera</h3>
                <div class="status-indicator" id="camera-status"></div>
            </div>
            <div class="status-item">
                <h3>Detector</h3>
                <div class="status-indicator" id="detector-status"></div>
            </div>
            <div class="status-item">
                <h3>Database</h3>
                <div class="status-indicator" id="database-status"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-start" id="start-btn" onclick="startMonitoring()">
                🚀 Start Monitoring
            </button>
            <button class="btn btn-stop" id="stop-btn" onclick="stopMonitoring()">
                ⏹️ Stop Monitoring
            </button>
        </div>

        <div id="message-container"></div>

        <div class="main-content">
            <div class="video-section">
                <h2>📹 Live Feed</h2>
                <div class="video-container">
                    <div class="video-placeholder" id="video-placeholder">
                        Camera feed will appear here when monitoring starts
                    </div>
                    <img class="video-feed" id="video-feed" style="display: none;">
                </div>
                <div>
                    <strong>Current Detections:</strong>
                    <div class="detection-list" id="detection-list">
                        <div class="loading">No detections yet</div>
                    </div>
                </div>
            </div>

            <div class="detection-info">
                <h2>🎯 Recent Alerts</h2>
                <div class="detection-list" id="alerts-list">
                    <div class="loading">Loading alerts...</div>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h2>📊 Daily Statistics</h2>
            <div class="stats-grid" id="stats-grid">
                <div class="loading">Loading statistics...</div>
            </div>
        </div>
    </div>

    <script>
        let isMonitoring = false;
        let updateInterval = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStatus();
            loadRecentAlerts();
            loadDailyStats();
            
            // Set up periodic updates
            setInterval(updateSystemStatus, 5000);
            setInterval(loadRecentAlerts, 10000);
            setInterval(loadDailyStats, 30000);
        });

        function showMessage(message, type = 'info') {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                container.removeChild(messageDiv);
            }, 5000);
        }

        function updateSystemStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const status = data.status;
                        
                        // Update status indicators
                        document.getElementById('monitoring-status').className = 
                            status.monitoring_active ? 'status-indicator status-active' : 'status-indicator status-inactive';
                        document.getElementById('camera-status').className = 
                            status.camera_active ? 'status-indicator status-active' : 'status-indicator status-inactive';
                        document.getElementById('detector-status').className = 
                            status.detector_loaded ? 'status-indicator status-active' : 'status-indicator status-inactive';
                        document.getElementById('database-status').className = 
                            status.database_connected ? 'status-indicator status-active' : 'status-indicator status-inactive';
                        
                        isMonitoring = status.monitoring_active;
                        
                        // Update video feed
                        if (isMonitoring) {
                            updateVideoFeed();
                            updateDetections();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating system status:', error);
                });
        }

        function updateVideoFeed() {
            fetch('/api/current_frame')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.frame) {
                        const videoFeed = document.getElementById('video-feed');
                        const placeholder = document.getElementById('video-placeholder');
                        
                        videoFeed.src = data.frame;
                        videoFeed.style.display = 'block';
                        placeholder.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error updating video feed:', error);
                });
        }

        function updateDetections() {
            fetch('/api/latest_detections')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const detectionList = document.getElementById('detection-list');
                        
                        if (data.detections.length > 0) {
                            detectionList.innerHTML = '';
                            data.detections.forEach(detection => {
                                const item = document.createElement('div');
                                item.className = detection.alert ? 'detection-item detection-alert' : 'detection-item';
                                item.innerHTML = `
                                    <div>
                                        <strong>${detection.class}</strong>
                                        <div style="font-size: 0.9rem; color: #ccc;">
                                            Confidence: ${(detection.confidence * 100).toFixed(1)}%
                                        </div>
                                    </div>
                                    <div style="font-size: 0.8rem; color: #ccc;">
                                        ${new Date(detection.timestamp).toLocaleTimeString()}
                                    </div>
                                `;
                                detectionList.appendChild(item);
                            });
                        } else {
                            detectionList.innerHTML = '<div class="loading">No current detections</div>';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating detections:', error);
                });
        }

        function startMonitoring() {
            const startBtn = document.getElementById('start-btn');
            startBtn.disabled = true;
            startBtn.textContent = 'Starting...';
            
            fetch('/api/start_monitoring', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    isMonitoring = true;
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error starting monitoring: ' + error, 'error');
            })
            .finally(() => {
                startBtn.disabled = false;
                startBtn.textContent = '🚀 Start Monitoring';
            });
        }

        function stopMonitoring() {
            const stopBtn = document.getElementById('stop-btn');
            stopBtn.disabled = true;
            stopBtn.textContent = 'Stopping...';
            
            fetch('/api/stop_monitoring', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    isMonitoring = false;
                    
                    // Hide video feed
                    const videoFeed = document.getElementById('video-feed');
                    const placeholder = document.getElementById('video-placeholder');
                    videoFeed.style.display = 'none';
                    placeholder.style.display = 'flex';
                    placeholder.textContent = 'Camera feed will appear here when monitoring starts';
                    
                    // Clear detections
                    document.getElementById('detection-list').innerHTML = '<div class="loading">No detections yet</div>';
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error stopping monitoring: ' + error, 'error');
            })
            .finally(() => {
                stopBtn.disabled = false;
                stopBtn.textContent = '⏹️ Stop Monitoring';
            });
        }

        function loadRecentAlerts() {
            fetch('/api/recent_alerts?hours=24&limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const alertsList = document.getElementById('alerts-list');
                        
                        if (data.alerts.length > 0) {
                            alertsList.innerHTML = '';
                            data.alerts.forEach(alert => {
                                const item = document.createElement('div');
                                item.className = `alert-item alert-${alert.alert_level.toLowerCase()}`;
                                item.innerHTML = `
                                    <div>
                                        <strong>${alert.object_class}</strong> detected
                                        <div style="font-size: 0.9rem; opacity: 0.8;">
                                            Confidence: ${(alert.confidence * 100).toFixed(1)}%
                                        </div>
                                        <div class="alert-timestamp">
                                            ${new Date(alert.timestamp).toLocaleString()}
                                        </div>
                                    </div>
                                `;
                                alertsList.appendChild(item);
                            });
                        } else {
                            alertsList.innerHTML = '<div class="loading">No recent alerts</div>';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading alerts:', error);
                    document.getElementById('alerts-list').innerHTML = '<div class="loading">Error loading alerts</div>';
                });
        }

        function loadDailyStats() {
            fetch('/api/daily_stats?days=7')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const statsGrid = document.getElementById('stats-grid');
                        
                        if (data.stats.length > 0) {
                            const latest = data.stats[0];
                            statsGrid.innerHTML = `
                                <div class="stat-card">
                                    <div class="stat-number">${latest.total_detections || 0}</div>
                                    <div>Total Detections Today</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${latest.person_detections || 0}</div>
                                    <div>Person Detections</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${latest.vehicle_detections || 0}</div>
                                    <div>Vehicle Detections</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">${latest.alert_count || 0}</div>
                                    <div>Security Alerts</div>
                                </div>
                            `;
                        } else {
                            statsGrid.innerHTML = `
                                <div class="stat-card">
                                    <div class="stat-number">0</div>
                                    <div>Total Detections Today</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">0</div>
                                    <div>Person Detections</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">0</div>
                                    <div>Vehicle Detections</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">0</div>
                                    <div>Security Alerts</div>
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading daily stats:', error);
                });
        }
    </script>
</body>
</html>