#!/usr/bin/env python3
"""
Optimized Camera Manager for Smart Security System
This module provides high-performance camera capture with minimal lag
"""

import cv2
import threading
import queue
import time
import numpy as np
from datetime import datetime, timedelta
import logging

class OptimizedCameraManager:
    """High-performance camera manager with threading and optimization"""
    
    def __init__(self, config):
        self.config = config
        self.cap = None
        self.is_active = False
        self.is_capturing = False
        
        # Threading components
        self.capture_thread = None
        self.frame_queue = queue.Queue(maxsize=config.FRAME_BUFFER_SIZE)
        self.latest_frame = None
        self.frame_lock = threading.Lock()
        
        # Performance tracking
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        self.dropped_frames = 0
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def initialize_camera(self, source=None):
        """Initialize camera with optimized settings"""
        source = source if source is not None else self.config.CAMERA_SOURCE
        
        try:
            # Initialize with specific backend for better performance
            if hasattr(self.config, 'CAMERA_BACKEND'):
                self.cap = cv2.VideoCapture(source, self.config.CAMERA_BACKEND)
            else:
                self.cap = cv2.VideoCapture(source)
            
            if not self.cap.isOpened():
                self.logger.error(f"Failed to open camera source: {source}")
                return False
            
            # Apply optimized camera settings
            self._apply_camera_settings()
            
            self.logger.info(f"Camera initialized successfully with source: {source}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing camera: {e}")
            return False
    
    def _apply_camera_settings(self):
        """Apply optimized camera settings for better performance"""
        try:
            # Set resolution
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.CAMERA_WIDTH)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.CAMERA_HEIGHT)
            
            # Set FPS
            self.cap.set(cv2.CAP_PROP_FPS, self.config.CAMERA_FPS)
            
            # Reduce buffer size to minimize lag
            if hasattr(self.config, 'CAMERA_BUFFER_SIZE'):
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, self.config.CAMERA_BUFFER_SIZE)
            
            # Set codec for better performance
            if hasattr(self.config, 'CAMERA_FOURCC'):
                self.cap.set(cv2.CAP_PROP_FOURCC, self.config.CAMERA_FOURCC)
            
            # Manual camera controls for consistent performance
            if hasattr(self.config, 'CAMERA_AUTO_EXPOSURE'):
                self.cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, self.config.CAMERA_AUTO_EXPOSURE)
            
            if hasattr(self.config, 'CAMERA_BRIGHTNESS'):
                self.cap.set(cv2.CAP_PROP_BRIGHTNESS, self.config.CAMERA_BRIGHTNESS)
            
            if hasattr(self.config, 'CAMERA_CONTRAST'):
                self.cap.set(cv2.CAP_PROP_CONTRAST, self.config.CAMERA_CONTRAST)
            
            if hasattr(self.config, 'CAMERA_SATURATION'):
                self.cap.set(cv2.CAP_PROP_SATURATION, self.config.CAMERA_SATURATION)
            
            # Disable auto-focus for consistent performance
            self.cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
            
            self.logger.info("Camera settings applied successfully")
            
        except Exception as e:
            self.logger.warning(f"Some camera settings could not be applied: {e}")
    
    def start_capture(self):
        """Start threaded camera capture"""
        if self.is_capturing:
            self.logger.warning("Camera capture is already running")
            return True
        
        if not self.cap or not self.cap.isOpened():
            self.logger.error("Camera not initialized")
            return False
        
        self.is_capturing = True
        self.is_active = True
        
        # Start capture thread
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        
        self.logger.info("Camera capture started")
        return True
    
    def stop_capture(self):
        """Stop camera capture and cleanup"""
        if not self.is_capturing:
            return
        
        self.is_capturing = False
        self.is_active = False
        
        # Wait for capture thread to finish
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2)
        
        # Cleanup camera
        if self.cap:
            self.cap.release()
            self.cap = None
        
        # Clear frame queue
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except queue.Empty:
                break
        
        self.logger.info("Camera capture stopped")
    
    def _capture_loop(self):
        """Main capture loop running in separate thread"""
        frame_time = 1.0 / self.config.CAMERA_FPS
        last_frame_time = time.time()
        
        while self.is_capturing:
            try:
                ret, frame = self.cap.read()
                current_time = time.time()
                
                if not ret:
                    self.logger.warning("Failed to capture frame")
                    time.sleep(0.01)
                    continue
                
                # Frame rate control
                elapsed = current_time - last_frame_time
                if elapsed < frame_time:
                    time.sleep(frame_time - elapsed)
                
                # Update latest frame with thread safety
                with self.frame_lock:
                    self.latest_frame = frame.copy()
                
                # Add frame to queue (non-blocking)
                try:
                    if self.config.ENABLE_FRAME_DROPPING and self.frame_queue.full():
                        # Drop oldest frame if queue is full
                        try:
                            self.frame_queue.get_nowait()
                            self.dropped_frames += 1
                        except queue.Empty:
                            pass
                    
                    # Add timestamp to frame metadata
                    frame_data = {
                        'frame': frame,
                        'timestamp': current_time,
                        'frame_id': self.fps_counter
                    }
                    
                    self.frame_queue.put_nowait(frame_data)
                    
                except queue.Full:
                    self.dropped_frames += 1
                
                # Update FPS counter
                self.fps_counter += 1
                if current_time - self.fps_start_time >= 1.0:
                    self.current_fps = self.fps_counter / (current_time - self.fps_start_time)
                    self.fps_counter = 0
                    self.fps_start_time = current_time
                
                last_frame_time = current_time
                
            except Exception as e:
                self.logger.error(f"Error in capture loop: {e}")
                time.sleep(0.1)
    
    def get_latest_frame(self):
        """Get the most recent frame (non-blocking)"""
        with self.frame_lock:
            if self.latest_frame is not None:
                return self.latest_frame.copy()
        return None
    
    def get_frame_from_queue(self, timeout=0.1):
        """Get frame from processing queue"""
        try:
            frame_data = self.frame_queue.get(timeout=timeout)
            
            # Check frame age if enabled
            if self.config.ENABLE_FRAME_DROPPING:
                frame_age_ms = (time.time() - frame_data['timestamp']) * 1000
                if frame_age_ms > self.config.MAX_FRAME_AGE_MS:
                    self.dropped_frames += 1
                    return None
            
            return frame_data
            
        except queue.Empty:
            return None
    
    def get_performance_stats(self):
        """Get camera performance statistics"""
        return {
            'fps': round(self.current_fps, 2),
            'dropped_frames': self.dropped_frames,
            'queue_size': self.frame_queue.qsize(),
            'is_active': self.is_active,
            'is_capturing': self.is_capturing
        }
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.dropped_frames = 0
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
    
    def __del__(self):
        """Cleanup on destruction"""
        self.stop_capture()
