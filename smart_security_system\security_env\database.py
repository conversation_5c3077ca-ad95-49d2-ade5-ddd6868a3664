import sqlite3
import json
from datetime import datetime, timedelta
import os

class SecurityDatabase:
    def __init__(self, db_path='security_system.db'):
        """Initialize the security database"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Create database tables if they don't exist"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Security alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                object_class TEXT NOT NULL,
                confidence REAL NOT NULL,
                bbox TEXT,
                alert_level TEXT,
                image_path TEXT,
                acknowledged BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # System logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Detection statistics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS detection_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                total_detections INTEGER DEFAULT 0,
                person_detections INTEGER DEFAULT 0,
                vehicle_detections INTEGER DEFAULT 0,
                alert_count INTEGER DEFAULT 0,
                avg_confidence REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("Database initialized successfully")
    
    def log_alert(self, detection_data, image_path=None):
        """
        Log a security alert
        
        Args:
            detection_data: Dictionary containing detection information
            image_path: Path to saved image (optional)
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO security_alerts 
                (timestamp, object_class, confidence, bbox, alert_level, image_path)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                detection_data['timestamp'],
                detection_data['class'],
                detection_data['confidence'],
                json.dumps(detection_data['bbox']),
                self._get_alert_level(detection_data['class']),
                image_path
            ))
            
            alert_id = cursor.lastrowid
            conn.commit()
            
            # Update daily statistics
            self._update_daily_stats(detection_data['class'])
            
            return alert_id
            
        except Exception as e:
            print(f"Error logging alert: {e}")
            return None
        finally:
            conn.close()
    
    def log_system_event(self, level, message, details=None):
        """
        Log a system event
        
        Args:
            level: Log level (INFO, WARNING, ERROR)
            message: Log message
            details: Additional details (optional)
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO system_logs (timestamp, level, message, details)
                VALUES (?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                level,
                message,
                details
            ))
            
            conn.commit()
            
        except Exception as e:
            print(f"Error logging system event: {e}")
        finally:
            conn.close()
    
    def get_recent_alerts(self, limit=50, hours=24):
        """
        Get recent security alerts
        
        Args:
            limit: Maximum number of alerts to return
            hours: Number of hours to look back
            
        Returns:
            List of alert dictionaries
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Calculate cutoff time
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            cursor.execute('''
                SELECT * FROM security_alerts 
                WHERE created_at >= ?
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (cutoff_time.isoformat(), limit))
            
            columns = [desc[0] for desc in cursor.description]
            alerts = []
            
            for row in cursor.fetchall():
                alert = dict(zip(columns, row))
                if alert['bbox']:
                    alert['bbox'] = json.loads(alert['bbox'])
                alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            print(f"Error retrieving alerts: {e}")
            return []
        finally:
            conn.close()
    
    def get_daily_stats(self, days=7):
        """
        Get daily detection statistics
        
        Args:
            days: Number of days to retrieve
            
        Returns:
            List of daily statistics
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT * FROM detection_stats 
                ORDER BY date DESC 
                LIMIT ?
            ''', (days,))
            
            columns = [desc[0] for desc in cursor.description]
            stats = []
            
            for row in cursor.fetchall():
                stats.append(dict(zip(columns, row)))
            
            return stats
            
        except Exception as e:
            print(f"Error retrieving statistics: {e}")
            return []
        finally:
            conn.close()
    
    def acknowledge_alert(self, alert_id):
        """
        Mark an alert as acknowledged
        
        Args:
            alert_id: ID of the alert to acknowledge
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                UPDATE security_alerts 
                SET acknowledged = TRUE 
                WHERE id = ?
            ''', (alert_id,))
            
            conn.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            print(f"Error acknowledging alert: {e}")
            return False
        finally:
            conn.close()
    
    def _get_alert_level(self, object_class):
        """
        Determine alert level based on object class
        
        Args:
            object_class: Detected object class
            
        Returns:
            Alert level string
        """
        high_priority = ['person']
        medium_priority = ['car', 'truck', 'bus', 'motorcycle']
        
        if object_class in high_priority:
            return 'HIGH'
        elif object_class in medium_priority:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _update_daily_stats(self, object_class):
        """
        Update daily detection statistics
        
        Args:
            object_class: Class of detected object
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            today = datetime.now().date().isoformat()
            
            # Check if today's stats exist
            cursor.execute('''
                SELECT id FROM detection_stats WHERE date = ?
            ''', (today,))
            
            if cursor.fetchone():
                # Update existing record
                if object_class == 'person':
                    cursor.execute('''
                        UPDATE detection_stats 
                        SET person_detections = person_detections + 1,
                            total_detections = total_detections + 1,
                            alert_count = alert_count + 1
                        WHERE date = ?
                    ''', (today,))
                elif object_class in ['car', 'truck', 'bus', 'motorcycle']:
                    cursor.execute('''
                        UPDATE detection_stats 
                        SET vehicle_detections = vehicle_detections + 1,
                            total_detections = total_detections + 1,
                            alert_count = alert_count + 1
                        WHERE date = ?
                    ''', (today,))
                else:
                    cursor.execute('''
                        UPDATE detection_stats 
                        SET total_detections = total_detections + 1
                        WHERE date = ?
                    ''', (today,))
            else:
                # Create new record
                person_count = 1 if object_class == 'person' else 0
                vehicle_count = 1 if object_class in ['car', 'truck', 'bus', 'motorcycle'] else 0
                alert_count = 1 if object_class in ['person', 'car', 'truck', 'bus', 'motorcycle'] else 0
                
                cursor.execute('''
                    INSERT INTO detection_stats 
                    (date, total_detections, person_detections, vehicle_detections, alert_count)
                    VALUES (?, ?, ?, ?, ?)
                ''', (today, 1, person_count, vehicle_count, alert_count))
            
            conn.commit()
            
        except Exception as e:
            print(f"Error updating daily stats: {e}")
        finally:
            conn.close()
    
    def cleanup_old_data(self, days_to_keep=30):
        """
        Clean up old data to prevent database bloat
        
        Args:
            days_to_keep: Number of days of data to retain
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            # Clean up old alerts
            cursor.execute('''
                DELETE FROM security_alerts 
                WHERE created_at < ?
            ''', (cutoff_date.isoformat(),))
            
            alerts_deleted = cursor.rowcount
            
            # Clean up old system logs
            cursor.execute('''
                DELETE FROM system_logs 
                WHERE created_at < ?
            ''', (cutoff_date.isoformat(),))
            
            logs_deleted = cursor.rowcount
            
            conn.commit()
            
            print(f"Cleanup completed: {alerts_deleted} alerts and {logs_deleted} logs removed")
            
        except Exception as e:
            print(f"Error during cleanup: {e}")
        finally:
            conn.close()

# Example usage
if __name__ == "__main__":
    db = SecurityDatabase()
    
    # Test the database
    print("Testing database operations...")
    
    # Log a test alert
    test_detection = {
        'timestamp': datetime.now().isoformat(),
        'class': 'person',
        'confidence': 0.85,
        'bbox': [100, 100, 200, 200]
    }
    
    alert_id = db.log_alert(test_detection)
    print(f"Test alert logged with ID: {alert_id}")
    
    # Log a system event
    db.log_system_event('INFO', 'System started', 'Test initialization')
    
    # Get recent alerts
    alerts = db.get_recent_alerts(limit=10)
    print(f"Recent alerts: {len(alerts)}")
    
    # Get daily stats
    stats = db.get_daily_stats(days=7)
    print(f"Daily stats: {len(stats)} days")
    
    print("Database test completed successfully!")