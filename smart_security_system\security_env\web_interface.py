from flask import Flask, render_template, jsonify, request, Response
import cv2
import threading
import time
import json
from datetime import datetime
import base64
import io
from PIL import Image
import numpy as np

from detector import SecurityDetector
from database import SecurityDatabase

app = Flask(__name__)

# Global variables
detector = None
database = None
camera = None
current_frame = None
is_monitoring = False
latest_detections = []

class CameraManager:
    def __init__(self):
        self.cap = None
        self.is_active = False
        self.lock = threading.Lock()
    
    def start_camera(self, source=0):
        """Start camera capture"""
        with self.lock:
            if not self.is_active:
                self.cap = cv2.VideoCapture(source)
                if self.cap.isOpened():
                    self.is_active = True
                    return True
                else:
                    return False
            return True
    
    def stop_camera(self):
        """Stop camera capture"""
        with self.lock:
            if self.is_active and self.cap:
                self.cap.release()
                self.is_active = False
    
    def get_frame(self):
        """Get current frame from camera"""
        with self.lock:
            if self.is_active and self.cap:
                ret, frame = self.cap.read()
                if ret:
                    return frame
        return None

def monitoring_thread():
    """Background thread for continuous monitoring"""
    global current_frame, latest_detections, is_monitoring
    
    frame_count = 0
    
    while is_monitoring:
        if camera and camera.is_active:
            frame = camera.get_frame()
            if frame is not None:
                frame_count += 1
                
                # Process every 3rd frame for performance
                if frame_count % 3 == 0:
                    processed_frame, detections, alert_triggered = detector.detect_objects(frame)
                    
                    # Update global variables
                    current_frame = processed_frame
                    latest_detections = detections
                    
                    # Log alerts to database
                    if alert_triggered:
                        for detection in detections:
                            if detection['alert']:
                                database.log_alert(detection)
                                print(f"Alert logged: {detection['class']} detected with confidence {detection['confidence']:.2f}")
                
                time.sleep(0.1)  # Small delay to prevent excessive CPU usage
        else:
            time.sleep(1)  # Wait longer if camera is not active

def encode_frame_to_base64(frame):
    """Convert OpenCV frame to base64 string"""
    if frame is None:
        return None
    
    _, buffer = cv2.imencode('.jpg', frame)
    img_str = base64.b64encode(buffer).decode('utf-8')
    return f"data:image/jpeg;base64,{img_str}"

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/start_monitoring', methods=['POST'])
def start_monitoring():
    """Start the security monitoring"""
    global is_monitoring, camera, detector, database
    
    try:
        if not is_monitoring:
            # Initialize components
            detector = SecurityDetector()
            database = SecurityDatabase()
            camera = CameraManager()
            
            # Start camera
            if camera.start_camera():
                is_monitoring = True
                
                # Start monitoring thread
                monitor_thread = threading.Thread(target=monitoring_thread)
                monitor_thread.daemon = True
                monitor_thread.start()
                
                database.log_system_event('INFO', 'Monitoring started')
                
                return jsonify({
                    'success': True,
                    'message': 'Security monitoring started successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Failed to start camera'
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Monitoring is already active'
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error starting monitoring: {str(e)}'
        })

@app.route('/api/stop_monitoring', methods=['POST'])
def stop_monitoring():
    """Stop the security monitoring"""
    global is_monitoring, camera, database
    
    try:
        if is_monitoring:
            is_monitoring = False
            
            if camera:
                camera.stop_camera()
            
            if database:
                database.log_system_event('INFO', 'Monitoring stopped')
            
            return jsonify({
                'success': True,
                'message': 'Security monitoring stopped successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Monitoring is not active'
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error stopping monitoring: {str(e)}'
        })

@app.route('/api/current_frame')
def get_current_frame():
    """Get the current processed frame"""
    global current_frame
    
    if current_frame is not None:
        encoded_frame = encode_frame_to_base64(current_frame)
        return jsonify({
            'success': True,
            'frame': encoded_frame,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'No frame available'
        })

@app.route('/api/latest_detections')
def get_latest_detections():
    """Get the latest detections"""
    global latest_detections
    
    return jsonify({
        'success': True,
        'detections': latest_detections,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/recent_alerts')
def get_recent_alerts():
    """Get recent security alerts"""
    hours = request.args.get('hours', 24, type=int)
    limit = request.args.get('limit', 50, type=int)
    
    if database:
        alerts = database.get_recent_alerts(limit=limit, hours=hours)
        return jsonify({
            'success': True,
            'alerts': alerts
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Database not initialized'
        })

@app.route('/api/daily_stats')
def get_daily_stats():
    """Get daily detection statistics"""
    days = request.args.get('days', 7, type=int)
    
    if database:
        stats = database.get_daily_stats(days=days)
        return jsonify({
            'success': True,
            'stats': stats
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Database not initialized'
        })

@app.route('/api/acknowledge_alert', methods=['POST'])
def acknowledge_alert():
    """Acknowledge a security alert"""
    data = request.get_json()
    alert_id = data.get('alert_id')
    
    if database and alert_id:
        success = database.acknowledge_alert(alert_id)
        if success:
            return jsonify({
                'success': True,
                'message': 'Alert acknowledged successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to acknowledge alert'
            })
    else:
        return jsonify({
            'success': False,
            'message': 'Invalid alert ID or database not initialized'
        })

@app.route('/api/status')
def get_system_status():
    """Get current system status"""
    global is_monitoring, camera, detector, database
    
    status = {
        'monitoring_active': is_monitoring,
        'camera_active': camera.is_active if camera else False,
        'detector_loaded': detector is not None,
        'database_connected': database is not None,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify({
        'success': True,
        'status': status
    })

@app.route('/video_feed')
def video_feed():
    """Video streaming route"""
    def generate():
        global current_frame
        while True:
            if current_frame is not None:
                ret, buffer = cv2.imencode('.jpg', current_frame)
                frame = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
            time.sleep(0.1)
    
    return Response(generate(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    import os
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("Starting Smart Security System Web Interface...")
    print("Access the dashboard at: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)