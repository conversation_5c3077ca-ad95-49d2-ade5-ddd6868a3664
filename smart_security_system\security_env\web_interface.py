from flask import Flask, render_template, jsonify, request, Response
import cv2
import threading
import time
import json
from datetime import datetime
import base64
import io
from PIL import Image
import numpy as np

from detector import SecurityDetector
from optimized_camera import OptimizedCameraManager
from optimized_detector import OptimizedSecurityDetector
from database import SecurityDatabase
from config import config

app = Flask(__name__)

# Global variables
detector = None
optimized_detector = None
database = None
camera = None
current_frame = None
is_monitoring = False
latest_detections = []
performance_stats = {}

def monitoring_thread():
    """Optimized background thread for continuous monitoring"""
    global current_frame, latest_detections, is_monitoring, performance_stats

    frame_count = 0
    last_stats_update = time.time()

    while is_monitoring:
        if camera and camera.is_active:
            # Get frame from optimized camera
            frame_data = camera.get_frame_from_queue(timeout=0.05)

            if frame_data is not None:
                frame_count += 1

                # Process every Nth frame based on config
                if frame_count % config.FRAME_SKIP == 0:
                    if config.ENABLE_ASYNC_PROCESSING and optimized_detector:
                        # Queue frame for async processing
                        optimized_detector.queue_frame_for_detection(frame_data)

                        # Get processed results
                        result = optimized_detector.get_detection_result()
                        if result:
                            current_frame = result['frame']
                            latest_detections = result['detections']

                            # Log alerts to database
                            if result['alert_triggered']:
                                for detection in result['detections']:
                                    if detection['alert']:
                                        database.log_alert(detection)
                                        print(f"Alert logged: {detection['class']} detected with confidence {detection['confidence']:.2f}")
                    else:
                        # Fallback to synchronous processing
                        processed_frame, detections, alert_triggered = detector.detect_objects(frame_data['frame'])
                        current_frame = processed_frame
                        latest_detections = detections

                        if alert_triggered:
                            for detection in detections:
                                if detection['alert']:
                                    database.log_alert(detection)
                else:
                    # Update current frame even if not processing
                    current_frame = frame_data['frame']

                # Update performance stats periodically
                if time.time() - last_stats_update > 1.0:
                    performance_stats.update({
                        'camera': camera.get_performance_stats() if camera else {},
                        'detector': optimized_detector.get_performance_stats() if optimized_detector else {}
                    })
                    last_stats_update = time.time()

                # Reduced delay for better responsiveness
                time.sleep(config.LOOP_DELAY)
        else:
            time.sleep(0.5)  # Wait if camera is not active

def encode_frame_to_base64(frame):
    """Convert OpenCV frame to base64 string"""
    if frame is None:
        return None
    
    _, buffer = cv2.imencode('.jpg', frame)
    img_str = base64.b64encode(buffer).decode('utf-8')
    return f"data:image/jpeg;base64,{img_str}"

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/start_monitoring', methods=['POST'])
def start_monitoring():
    """Start the optimized security monitoring"""
    global is_monitoring, camera, detector, optimized_detector, database

    try:
        if not is_monitoring:
            # Initialize components
            detector = SecurityDetector()
            optimized_detector = OptimizedSecurityDetector(config)
            database = SecurityDatabase()
            camera = OptimizedCameraManager(config)

            # Initialize and start camera
            if camera.initialize_camera() and camera.start_capture():
                # Start async processing if enabled
                if config.ENABLE_ASYNC_PROCESSING:
                    optimized_detector.start_async_processing()
                    print("Async processing started")

                is_monitoring = True

                # Start monitoring thread
                monitor_thread = threading.Thread(target=monitoring_thread)
                monitor_thread.daemon = True
                monitor_thread.start()

                database.log_system_event('INFO', 'Optimized monitoring started')

                return jsonify({
                    'success': True,
                    'message': 'Optimized security monitoring started successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Failed to start optimized camera'
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Monitoring is already active'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error starting monitoring: {str(e)}'
        })

@app.route('/api/stop_monitoring', methods=['POST'])
def stop_monitoring():
    """Stop the optimized security monitoring"""
    global is_monitoring, camera, optimized_detector, database

    try:
        if is_monitoring:
            is_monitoring = False

            if camera:
                camera.stop_capture()

            if optimized_detector:
                optimized_detector.stop_async_processing()

            if database:
                database.log_system_event('INFO', 'Optimized monitoring stopped')

            return jsonify({
                'success': True,
                'message': 'Optimized security monitoring stopped successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Monitoring is not active'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error stopping monitoring: {str(e)}'
        })

@app.route('/api/current_frame')
def get_current_frame():
    """Get the current processed frame"""
    global current_frame
    
    if current_frame is not None:
        encoded_frame = encode_frame_to_base64(current_frame)
        return jsonify({
            'success': True,
            'frame': encoded_frame,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'message': 'No frame available'
        })

@app.route('/api/latest_detections')
def get_latest_detections():
    """Get the latest detections"""
    global latest_detections
    
    return jsonify({
        'success': True,
        'detections': latest_detections,
        'count': len(latest_detections),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/performance')
def get_performance():
    """Get performance statistics"""
    global performance_stats

    return jsonify({
        'success': True,
        'performance': performance_stats,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/recent_alerts')
def get_recent_alerts():
    """Get recent security alerts"""
    hours = request.args.get('hours', 24, type=int)
    limit = request.args.get('limit', 50, type=int)
    
    if database:
        alerts = database.get_recent_alerts(limit=limit, hours=hours)
        return jsonify({
            'success': True,
            'alerts': alerts
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Database not initialized'
        })

@app.route('/api/daily_stats')
def get_daily_stats():
    """Get daily detection statistics"""
    days = request.args.get('days', 7, type=int)
    
    if database:
        stats = database.get_daily_stats(days=days)
        return jsonify({
            'success': True,
            'stats': stats
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Database not initialized'
        })

@app.route('/api/acknowledge_alert', methods=['POST'])
def acknowledge_alert():
    """Acknowledge a security alert"""
    data = request.get_json()
    alert_id = data.get('alert_id')
    
    if database and alert_id:
        success = database.acknowledge_alert(alert_id)
        if success:
            return jsonify({
                'success': True,
                'message': 'Alert acknowledged successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to acknowledge alert'
            })
    else:
        return jsonify({
            'success': False,
            'message': 'Invalid alert ID or database not initialized'
        })

@app.route('/api/status')
def get_system_status():
    """Get current system status"""
    global is_monitoring, camera, detector, database
    
    status = {
        'monitoring_active': is_monitoring,
        'camera_active': camera.is_active if camera else False,
        'detector_loaded': detector is not None,
        'database_connected': database is not None,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify({
        'success': True,
        'status': status
    })

@app.route('/video_feed')
def video_feed():
    """Video streaming route"""
    def generate():
        global current_frame
        while True:
            if current_frame is not None:
                _, buffer = cv2.imencode('.jpg', current_frame)
                frame = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
            time.sleep(0.1)
    
    return Response(generate(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    import os
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    print("Starting Smart Security System Web Interface...")
    print("Access the dashboard at: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)