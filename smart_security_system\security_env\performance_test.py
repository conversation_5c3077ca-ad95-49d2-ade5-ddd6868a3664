#!/usr/bin/env python3
"""
Performance Test Script for Optimized Security System
This script tests and compares the performance of the optimized camera and detector
"""

import time
import cv2
import threading
from datetime import datetime
import statistics

from config import config
from optimized_camera import OptimizedCameraManager
from optimized_detector import OptimizedSecurityDetector
from detector import SecurityDetector

class PerformanceTest:
    """Performance testing class"""
    
    def __init__(self):
        self.results = {
            'original': {'fps': [], 'processing_times': []},
            'optimized': {'fps': [], 'processing_times': []}
        }
    
    def test_original_system(self, duration=30):
        """Test original system performance"""
        print("Testing Original System Performance...")
        
        # Initialize original components
        detector = SecurityDetector()
        cap = cv2.VideoCapture(config.CAMERA_SOURCE)
        
        if not cap.isOpened():
            print("Error: Could not open camera for original test")
            return
        
        # Set basic camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, config.CAMERA_WIDTH)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, config.CAMERA_HEIGHT)
        cap.set(cv2.CAP_PROP_FPS, config.CAMERA_FPS)
        
        start_time = time.time()
        frame_count = 0
        processing_times = []
        
        while time.time() - start_time < duration:
            ret, frame = cap.read()
            if not ret:
                continue
            
            frame_count += 1
            
            # Process every 3rd frame (original behavior)
            if frame_count % 3 == 0:
                process_start = time.time()
                _, _, _ = detector.detect_objects(frame)
                process_time = time.time() - process_start
                processing_times.append(process_time)
            
            time.sleep(0.1)  # Original delay
        
        cap.release()
        
        # Calculate results
        total_time = time.time() - start_time
        fps = frame_count / total_time
        
        self.results['original']['fps'] = fps
        self.results['original']['processing_times'] = processing_times
        
        print(f"Original System - FPS: {fps:.2f}, Avg Processing Time: {statistics.mean(processing_times)*1000:.2f}ms")
    
    def test_optimized_system(self, duration=30):
        """Test optimized system performance"""
        print("Testing Optimized System Performance...")
        
        # Initialize optimized components
        camera = OptimizedCameraManager(config)
        detector = OptimizedSecurityDetector(config)
        
        if not camera.initialize_camera():
            print("Error: Could not initialize optimized camera")
            return
        
        if not camera.start_capture():
            print("Error: Could not start optimized camera capture")
            return
        
        # Start async processing
        if config.ENABLE_ASYNC_PROCESSING:
            detector.start_async_processing()
        
        start_time = time.time()
        frame_count = 0
        processing_times = []
        
        while time.time() - start_time < duration:
            # Get frame from optimized camera
            frame_data = camera.get_frame_from_queue(timeout=0.1)
            
            if frame_data is not None:
                frame_count += 1
                
                # Process every Nth frame based on config
                if frame_count % config.FRAME_SKIP == 0:
                    if config.ENABLE_ASYNC_PROCESSING:
                        # Queue for async processing
                        detector.queue_frame_for_detection(frame_data)
                        
                        # Get result
                        result = detector.get_detection_result()
                        if result:
                            processing_times.append(result['processing_time'])
                    else:
                        # Synchronous processing
                        process_start = time.time()
                        _, _, _ = detector.detect_objects_sync(frame_data['frame'])
                        process_time = time.time() - process_start
                        processing_times.append(process_time)
            
            time.sleep(config.LOOP_DELAY)  # Optimized delay
        
        # Cleanup
        camera.stop_capture()
        if config.ENABLE_ASYNC_PROCESSING:
            detector.stop_async_processing()
        
        # Calculate results
        total_time = time.time() - start_time
        fps = frame_count / total_time
        
        self.results['optimized']['fps'] = fps
        self.results['optimized']['processing_times'] = processing_times
        
        print(f"Optimized System - FPS: {fps:.2f}, Avg Processing Time: {statistics.mean(processing_times)*1000:.2f}ms")
    
    def run_comparison_test(self, duration=30):
        """Run comparison test between original and optimized systems"""
        print("=" * 60)
        print("SECURITY SYSTEM PERFORMANCE COMPARISON")
        print("=" * 60)
        
        # Test original system
        self.test_original_system(duration)
        time.sleep(2)  # Brief pause between tests
        
        # Test optimized system
        self.test_optimized_system(duration)
        
        # Print comparison results
        self.print_comparison_results()
    
    def print_comparison_results(self):
        """Print detailed comparison results"""
        print("\n" + "=" * 60)
        print("PERFORMANCE COMPARISON RESULTS")
        print("=" * 60)
        
        orig_fps = self.results['original']['fps']
        opt_fps = self.results['optimized']['fps']
        
        orig_proc_times = self.results['original']['processing_times']
        opt_proc_times = self.results['optimized']['processing_times']
        
        if orig_proc_times and opt_proc_times:
            orig_avg_proc = statistics.mean(orig_proc_times) * 1000
            opt_avg_proc = statistics.mean(opt_proc_times) * 1000
            
            print(f"FPS Comparison:")
            print(f"  Original System:  {orig_fps:.2f} FPS")
            print(f"  Optimized System: {opt_fps:.2f} FPS")
            print(f"  Improvement:      {((opt_fps - orig_fps) / orig_fps * 100):+.1f}%")
            
            print(f"\nProcessing Time Comparison:")
            print(f"  Original System:  {orig_avg_proc:.2f}ms avg")
            print(f"  Optimized System: {opt_avg_proc:.2f}ms avg")
            print(f"  Improvement:      {((orig_avg_proc - opt_avg_proc) / orig_avg_proc * 100):+.1f}%")
            
            print(f"\nFrame Processing Statistics:")
            print(f"  Original - Min: {min(orig_proc_times)*1000:.2f}ms, Max: {max(orig_proc_times)*1000:.2f}ms")
            print(f"  Optimized - Min: {min(opt_proc_times)*1000:.2f}ms, Max: {max(opt_proc_times)*1000:.2f}ms")
            
            print(f"\nConfiguration Used:")
            print(f"  Camera Resolution: {config.CAMERA_WIDTH}x{config.CAMERA_HEIGHT}")
            print(f"  Target FPS: {config.CAMERA_FPS}")
            print(f"  Frame Skip: {config.FRAME_SKIP}")
            print(f"  Async Processing: {config.ENABLE_ASYNC_PROCESSING}")
            print(f"  GPU Enabled: {config.USE_GPU}")
            print(f"  Processing Threads: {config.NUM_THREADS}")
        
        print("=" * 60)

def main():
    """Main function to run performance tests"""
    print("Security System Performance Test")
    print("This will test both original and optimized systems")
    print("Make sure your camera is connected and working")
    
    # Ask user for test duration
    try:
        duration = int(input("Enter test duration in seconds (default 30): ") or "30")
    except ValueError:
        duration = 30
    
    # Create and run test
    test = PerformanceTest()
    test.run_comparison_test(duration)
    
    print("\nTest completed! Check the results above.")
    print("The optimized system should show:")
    print("- Higher FPS (less lag)")
    print("- Lower processing times")
    print("- More consistent performance")

if __name__ == "__main__":
    main()
