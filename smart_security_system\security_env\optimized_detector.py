#!/usr/bin/env python3
"""
Optimized Security Detector with Async Processing
This module provides high-performance object detection with minimal lag
"""

import cv2
import torch
from ultralytics import YOLO
import numpy as np
import time
import threading
import queue
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor
import asyncio

class OptimizedSecurityDetector:
    """High-performance security detector with async processing"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize YOLO model
        self._initialize_model()
        
        # Threading components for async processing
        self.detection_queue = queue.Queue(maxsize=config.DETECTION_QUEUE_SIZE)
        self.result_queue = queue.Queue(maxsize=config.DETECTION_QUEUE_SIZE)
        self.processing_threads = []
        self.is_processing = False
        
        # Performance tracking
        self.detection_count = 0
        self.processing_times = []
        self.avg_processing_time = 0
        
        # Detection results cache
        self.last_detections = []
        self.last_detection_time = 0
        
        # Thread pool for parallel processing
        if config.ENABLE_ASYNC_PROCESSING:
            self.executor = ThreadPoolExecutor(max_workers=config.NUM_THREADS)
        else:
            self.executor = None
    
    def _initialize_model(self):
        """Initialize YOLO model with optimizations"""
        try:
            self.logger.info(f"Loading YOLO model: {self.config.YOLO_MODEL_PATH}")
            self.model = YOLO(self.config.YOLO_MODEL_PATH)
            
            # Configure model for performance
            if self.config.USE_GPU and torch.cuda.is_available():
                self.model.to('cuda')
                self.logger.info("GPU acceleration enabled")
            else:
                self.model.to('cpu')
                self.logger.info("Using CPU for inference")
            
            # Warm up the model
            dummy_frame = np.zeros((self.config.CAMERA_HEIGHT, self.config.CAMERA_WIDTH, 3), dtype=np.uint8)
            _ = self.model(dummy_frame, conf=self.config.DETECTION_CONFIDENCE, verbose=False)
            
            self.logger.info("Model initialized and warmed up successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing model: {e}")
            raise
    
    def start_async_processing(self):
        """Start asynchronous detection processing"""
        if self.is_processing:
            return
        
        self.is_processing = True
        
        # Start processing threads
        for i in range(self.config.NUM_THREADS):
            thread = threading.Thread(target=self._processing_loop, daemon=True)
            thread.start()
            self.processing_threads.append(thread)
        
        self.logger.info(f"Started {self.config.NUM_THREADS} processing threads")
    
    def stop_async_processing(self):
        """Stop asynchronous processing"""
        self.is_processing = False
        
        # Wait for threads to finish
        for thread in self.processing_threads:
            if thread.is_alive():
                thread.join(timeout=1)
        
        self.processing_threads.clear()
        
        if self.executor:
            self.executor.shutdown(wait=True)
        
        self.logger.info("Async processing stopped")
    
    def _processing_loop(self):
        """Main processing loop for detection threads"""
        while self.is_processing:
            try:
                # Get frame from queue
                frame_data = self.detection_queue.get(timeout=0.1)
                
                if frame_data is None:
                    continue
                
                # Process detection
                start_time = time.time()
                processed_frame, detections, alert_triggered = self._detect_objects_optimized(
                    frame_data['frame']
                )
                processing_time = time.time() - start_time
                
                # Update performance stats
                self.processing_times.append(processing_time)
                if len(self.processing_times) > 100:
                    self.processing_times.pop(0)
                self.avg_processing_time = sum(self.processing_times) / len(self.processing_times)
                
                # Store results
                result_data = {
                    'frame': processed_frame,
                    'detections': detections,
                    'alert_triggered': alert_triggered,
                    'timestamp': frame_data['timestamp'],
                    'frame_id': frame_data['frame_id'],
                    'processing_time': processing_time
                }
                
                # Add to result queue (non-blocking)
                try:
                    self.result_queue.put_nowait(result_data)
                except queue.Full:
                    # Remove oldest result if queue is full
                    try:
                        self.result_queue.get_nowait()
                        self.result_queue.put_nowait(result_data)
                    except queue.Empty:
                        pass
                
                self.detection_count += 1
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error in processing loop: {e}")
    
    def queue_frame_for_detection(self, frame_data):
        """Queue frame for asynchronous detection"""
        if not self.is_processing:
            return False
        
        try:
            # Add frame to detection queue (non-blocking)
            self.detection_queue.put_nowait(frame_data)
            return True
        except queue.Full:
            # Queue is full, optionally drop oldest frame
            if self.config.ENABLE_FRAME_DROPPING:
                try:
                    self.detection_queue.get_nowait()
                    self.detection_queue.put_nowait(frame_data)
                    return True
                except queue.Empty:
                    pass
            return False
    
    def get_detection_result(self, timeout=0.01):
        """Get detection result from result queue"""
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def detect_objects_sync(self, frame):
        """Synchronous object detection (for compatibility)"""
        return self._detect_objects_optimized(frame)
    
    def _detect_objects_optimized(self, frame):
        """Optimized object detection with performance improvements"""
        try:
            # Resize frame if needed for faster processing
            if self.config.OPTIMIZE_FOR_SPEED:
                original_frame = frame.copy()
                # Reduce resolution for detection, then scale back results
                scale_factor = 0.75
                small_frame = cv2.resize(frame, None, fx=scale_factor, fy=scale_factor)
                detection_frame = small_frame
            else:
                original_frame = frame
                detection_frame = frame
                scale_factor = 1.0
            
            # Run YOLO detection with optimized parameters
            results = self.model(
                detection_frame,
                conf=self.config.DETECTION_CONFIDENCE,
                iou=self.config.IOU_THRESHOLD,
                max_det=self.config.MAX_DETECTIONS,
                verbose=False,
                stream=False
            )
            
            detections = []
            alert_triggered = False
            
            # Process results
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract box coordinates
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        confidence = float(box.conf[0])
                        class_id = int(box.cls[0])
                        
                        # Scale coordinates back if frame was resized
                        if scale_factor != 1.0:
                            x1 = int(x1 / scale_factor)
                            y1 = int(y1 / scale_factor)
                            x2 = int(x2 / scale_factor)
                            y2 = int(y2 / scale_factor)
                        
                        # Only process classes we care about
                        if class_id in self.config.SECURITY_CLASSES:
                            class_name = self.config.SECURITY_CLASSES[class_id]
                            
                            # Check if this should trigger an alert
                            is_alert = class_id in self.config.ALERT_CLASSES
                            if is_alert:
                                alert_triggered = True
                                color = (0, 0, 255)  # Red for alerts
                                thickness = 3
                            else:
                                color = (0, 255, 0)  # Green for normal objects
                                thickness = 2
                            
                            # Draw bounding box on original frame
                            cv2.rectangle(original_frame, (x1, y1), (x2, y2), color, thickness)
                            
                            # Draw label
                            label = f"{class_name}: {confidence:.2f}"
                            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                            cv2.rectangle(original_frame, (x1, y1 - label_size[1] - 10), 
                                        (x1 + label_size[0], y1), color, -1)
                            cv2.putText(original_frame, label, (x1, y1 - 5), 
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                            
                            # Store detection info
                            detections.append({
                                'class': class_name,
                                'class_id': class_id,
                                'confidence': confidence,
                                'bbox': [x1, y1, x2, y2],
                                'timestamp': datetime.now().isoformat(),
                                'alert': is_alert,
                                'alert_level': self.config.ALERT_LEVELS.get(class_name, 'LOW')
                            })
            
            # Cache results for quick access
            self.last_detections = detections
            self.last_detection_time = time.time()
            
            return original_frame, detections, alert_triggered
            
        except Exception as e:
            self.logger.error(f"Error in object detection: {e}")
            return frame, [], False
    
    def get_performance_stats(self):
        """Get detector performance statistics"""
        return {
            'detection_count': self.detection_count,
            'avg_processing_time': round(self.avg_processing_time * 1000, 2),  # in ms
            'detection_queue_size': self.detection_queue.qsize(),
            'result_queue_size': self.result_queue.qsize(),
            'is_processing': self.is_processing,
            'gpu_available': torch.cuda.is_available(),
            'gpu_enabled': self.config.USE_GPU and torch.cuda.is_available()
        }
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.detection_count = 0
        self.processing_times.clear()
        self.avg_processing_time = 0
    
    def __del__(self):
        """Cleanup on destruction"""
        self.stop_async_processing()
