#!/usr/bin/env python3
"""
Smart Home Security System Configuration
This file contains all configuration settings for the security system
"""

import os
import json
from pathlib import Path

class Config:
    """Configuration class for the Smart Home Security System"""
    
    def __init__(self, config_file=None):
        """Initialize configuration with default values"""
        self.CONFIG_FILE = config_file or 'config.json'
        self.set_defaults()
        self.load_config()
    
    def set_defaults(self):
        """Set default configuration values"""
        
        # ========================
        # SYSTEM CONFIGURATION
        # ========================
        self.DEBUG = False
        self.PROJECT_NAME = "Smart Home Security System"
        self.VERSION = "1.0.0"
        
        # ========================
        # DETECTION CONFIGURATION
        # ========================
        # YOLO Model Settings
        self.YOLO_MODEL_PATH = "yolov8n.pt"  # Options: yolov8n.pt, yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt
        self.DETECTION_CONFIDENCE = 0.5      # Minimum confidence threshold (0.0 - 1.0)
        self.IOU_THRESHOLD = 0.45            # IoU threshold for NMS
        self.MAX_DETECTIONS = 300            # Maximum number of detections per image
        
        # Detection Classes (COCO dataset class IDs)
        self.SECURITY_CLASSES = {
            0: 'person',
            1: 'bicycle',
            2: 'car',
            3: 'motorcycle',
            5: 'bus',
            7: 'truck',
            15: 'cat',
            16: 'dog',
            17: 'horse',
            18: 'sheep',
            19: 'cow',
            20: 'elephant',
            21: 'bear',
            22: 'zebra',
            23: 'giraffe'
        }
        
        # Classes that should trigger alerts
        self.ALERT_CLASSES = [0, 1, 2, 3, 5, 7]  # person, bicycle, car, motorcycle, bus, truck
        
        # Alert Level Configuration
        self.ALERT_LEVELS = {
            'person': 'HIGH',
            'bicycle': 'MEDIUM',
            'car': 'MEDIUM',
            'motorcycle': 'MEDIUM',
            'bus': 'MEDIUM',
            'truck': 'MEDIUM',
            'cat': 'LOW',
            'dog': 'LOW'
        }
        
        # ========================
        # CAMERA CONFIGURATION
        # ========================
        self.CAMERA_SOURCE = 0               # 0 for default webcam, or path to video file
        self.CAMERA_WIDTH = 640             # Camera resolution width
        self.CAMERA_HEIGHT = 480            # Camera resolution height
        self.CAMERA_FPS = 30                # Frames per second
        self.FRAME_SKIP = 3                 # Process every Nth frame (for performance)
        
        # Video Recording Settings
        self.ENABLE_VIDEO_RECORDING = True
        self.VIDEO_OUTPUT_PATH = "recordings/"
        self.VIDEO_FORMAT = "mp4v"          # Video codec
        self.VIDEO_QUALITY = 90             # Video quality (0-100)
        self.MAX_VIDEO_SIZE_MB = 1000       # Max video file size in MB
        
        # ========================
        # DATABASE CONFIGURATION
        # ========================
        self.DATABASE_PATH = "security_system.db"
        self.DATA_RETENTION_DAYS = 30       # Days to keep data before cleanup
        self.CLEANUP_INTERVAL = 3600        # Cleanup interval in seconds (1 hour)
        self.BACKUP_ENABLED = True
        self.BACKUP_INTERVAL_HOURS = 24     # Backup interval in hours
        self.BACKUP_PATH = "backups/"
        self.MAX_BACKUPS = 7                # Maximum number of backups to keep
        
        # ========================
        # WEB INTERFACE CONFIGURATION
        # ========================
        self.WEB_HOST = "0.0.0.0"          # Web interface host
        self.WEB_PORT = 5000               # Web interface port
        self.WEB_SECRET_KEY = "your-secret-key-change-this"  # Flask secret key
        self.WEB_THEME = "dark"            # UI theme: light, dark, auto
        self.ENABLE_WEB_AUTH = False       # Enable web authentication
        self.WEB_USERNAME = "admin"        # Web interface username
        self.WEB_PASSWORD = "admin"        # Web interface password
        
        # Static Files Configuration
        self.STATIC_PATH = "static/"
        self.TEMPLATE_PATH = "templates/"
        self.UPLOAD_PATH = "uploads/"
        self.MAX_UPLOAD_SIZE = 16 * 1024 * 1024  # 16MB
        
        # ========================
        # MONITORING CONFIGURATION
        # ========================
        self.LOOP_DELAY = 0.1              # Delay between monitoring loops (seconds)
        self.ALERT_COOLDOWN = 30           # Frames to wait before next alert
        self.DETECTION_TIMEOUT = 30        # Detection timeout in seconds
        self.MAX_PROCESSING_TIME = 5       # Maximum processing time per frame
        
        # Performance Settings
        self.USE_GPU = True                # Use GPU acceleration if available
        self.NUM_THREADS = 4               # Number of processing threads
        self.BATCH_SIZE = 1                # Batch size for processing
        self.MEMORY_LIMIT_MB = 1024        # Memory limit in MB
        
        # ========================
        # NOTIFICATION CONFIGURATION
        # ========================
        self.ENABLE_NOTIFICATIONS = True
        self.NOTIFICATION_TYPES = ["desktop", "email", "webhook"]
        
        # Email Notifications
        self.EMAIL_ENABLED = False
        self.EMAIL_SMTP_SERVER = "smtp.gmail.com"
        self.EMAIL_SMTP_PORT = 587
        self.EMAIL_USERNAME = "<EMAIL>"
        self.EMAIL_PASSWORD = "your-app-password"
        self.EMAIL_TO = "<EMAIL>"
        self.EMAIL_FROM = "<EMAIL>"
        
        # Webhook Notifications
        self.WEBHOOK_ENABLED = False
        self.WEBHOOK_URL = "https://your-webhook-url.com/alert"
        self.WEBHOOK_SECRET = "your-webhook-secret"
        
        # Desktop Notifications
        self.DESKTOP_NOTIFICATIONS = True
        self.NOTIFICATION_SOUND = True
        self.NOTIFICATION_DURATION = 5000   # Duration in milliseconds
        
        # ========================
        # LOGGING CONFIGURATION
        # ========================
        self.LOG_LEVEL = "INFO"            # DEBUG, INFO, WARNING, ERROR, CRITICAL
        self.LOG_FILE = "security_system.log"
        self.LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self.LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
        self.LOG_BACKUP_COUNT = 5
        self.CONSOLE_LOGGING = True
        
        # ========================
        # SECURITY CONFIGURATION
        # ========================
        self.ENABLE_MOTION_DETECTION = True
        self.MOTION_THRESHOLD = 500        # Motion detection threshold
        self.MOTION_BLUR_SIZE = 21         # Blur size for motion detection
        
        # Zone Configuration
        self.ENABLE_DETECTION_ZONES = False
        self.DETECTION_ZONES = [
            {
                "name": "entrance",
                "coordinates": [[100, 100], [300, 100], [300, 300], [100, 300]],
                "alert_level": "HIGH",
                "enabled": True
            },
            {
                "name": "driveway",
                "coordinates": [[400, 200], [600, 200], [600, 400], [400, 400]],
                "alert_level": "MEDIUM",
                "enabled": True
            }
        ]
        
        # Time-based Configuration
        self.ENABLE_SCHEDULE = False
        self.MONITORING_SCHEDULE = {
            "monday": {"start": "18:00", "end": "08:00"},
            "tuesday": {"start": "18:00", "end": "08:00"},
            "wednesday": {"start": "18:00", "end": "08:00"},
            "thursday": {"start": "18:00", "end": "08:00"},
            "friday": {"start": "18:00", "end": "08:00"},
            "saturday": {"start": "00:00", "end": "23:59"},
            "sunday": {"start": "00:00", "end": "23:59"}
        }
        
        # ========================
        # STORAGE CONFIGURATION
        # ========================
        self.SAVE_ALERT_IMAGES = True
        self.ALERT_IMAGE_PATH = "alert_images/"
        self.IMAGE_FORMAT = "jpg"
        self.IMAGE_QUALITY = 95
        self.MAX_IMAGE_SIZE = 1920, 1080    # Max image dimensions
        
        # Cleanup Settings
        self.AUTO_CLEANUP = True
        self.CLEANUP_SCHEDULE = "02:00"     # Daily cleanup time
        self.MIN_FREE_SPACE_GB = 5          # Minimum free space in GB
        
        # ========================
        # API CONFIGURATION
        # ========================
        self.ENABLE_API = True
        self.API_VERSION = "v1"
        self.API_RATE_LIMIT = 100           # Requests per minute
        self.API_KEY_REQUIRED = False
        self.API_KEYS = []                  # List of valid API keys
        
        # CORS Settings
        self.CORS_ENABLED = True
        self.CORS_ORIGINS = ["*"]
        self.CORS_METHODS = ["GET", "POST", "PUT", "DELETE"]
        
        # ========================
        # DEVELOPMENT CONFIGURATION
        # ========================
        self.MOCK_CAMERA = False            # Use mock camera for testing
        self.MOCK_DETECTIONS = False        # Generate fake detections for testing
        self.PROFILING_ENABLED = False      # Enable performance profiling
        self.BENCHMARK_MODE = False         # Enable benchmark mode
        
        # Testing Settings
        self.TEST_MODE = False
        self.TEST_DATA_PATH = "test_data/"
        self.TEST_OUTPUTS_PATH = "test_outputs/"
        
        # ========================
        # PATHS CONFIGURATION
        # ========================
        self.BASE_DIR = Path(__file__).parent
        self.DATA_DIR = self.BASE_DIR / "data"
        self.MODELS_DIR = self.BASE_DIR / "models"
        self.LOGS_DIR = self.BASE_DIR / "logs"
        self.TEMP_DIR = self.BASE_DIR / "temp"
        
        # Create directories if they don't exist
        self.create_directories()
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.DATA_DIR,
            self.MODELS_DIR,
            self.LOGS_DIR,
            self.TEMP_DIR,
            self.BASE_DIR / "static",
            self.BASE_DIR / "templates",
            self.BASE_DIR / "uploads",
            self.BASE_DIR / "recordings",
            self.BASE_DIR / "backups",
            self.BASE_DIR / "alert_images"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def load_config(self):
        """Load configuration from JSON file"""
        if os.path.exists(self.CONFIG_FILE):
            try:
                with open(self.CONFIG_FILE, 'r') as f:
                    config_data = json.load(f)
                    
                # Update configuration with loaded values
                for key, value in config_data.items():
                    if hasattr(self, key):
                        setattr(self, key, value)
                
                print(f"✅ Configuration loaded from {self.CONFIG_FILE}")
                
            except Exception as e:
                print(f"⚠️  Error loading configuration: {e}")
                print("Using default configuration values")
        else:
            print(f"⚠️  Configuration file {self.CONFIG_FILE} not found")
            print("Using default configuration values")
            # Create default config file
            self.save_config()
    
    def save_config(self):
        """Save current configuration to JSON file"""
        try:
            # Get all configuration attributes
            config_data = {}
            for attr in dir(self):
                if not attr.startswith('_') and not callable(getattr(self, attr)):
                    value = getattr(self, attr)
                    # Skip non-serializable objects
                    if isinstance(value, (str, int, float, bool, list, dict)):
                        config_data[attr] = value
                    elif isinstance(value, Path):
                        config_data[attr] = str(value)
            
            # Save to file
            with open(self.CONFIG_FILE, 'w') as f:
                json.dump(config_data, f, indent=4, sort_keys=True)
            
            print(f"✅ Configuration saved to {self.CONFIG_FILE}")
            
        except Exception as e:
            print(f"❌ Error saving configuration: {e}")
    
    def get_camera_config(self):
        """Get camera configuration dictionary"""
        return {
            'source': self.CAMERA_SOURCE,
            'width': self.CAMERA_WIDTH,
            'height': self.CAMERA_HEIGHT,
            'fps': self.CAMERA_FPS,
            'frame_skip': self.FRAME_SKIP
        }
    
    def get_detection_config(self):
        """Get detection configuration dictionary"""
        return {
            'model_path': self.YOLO_MODEL_PATH,
            'confidence': self.DETECTION_CONFIDENCE,
            'iou_threshold': self.IOU_THRESHOLD,
            'max_detections': self.MAX_DETECTIONS,
            'security_classes': self.SECURITY_CLASSES,
            'alert_classes': self.ALERT_CLASSES,
            'alert_levels': self.ALERT_LEVELS
        }
    
    def get_database_config(self):
        """Get database configuration dictionary"""
        return {
            'path': self.DATABASE_PATH,
            'retention_days': self.DATA_RETENTION_DAYS,
            'cleanup_interval': self.CLEANUP_INTERVAL,
            'backup_enabled': self.BACKUP_ENABLED,
            'backup_interval': self.BACKUP_INTERVAL_HOURS,
            'backup_path': self.BACKUP_PATH
        }
    
    def get_web_config(self):
        """Get web interface configuration dictionary"""
        return {
            'host': self.WEB_HOST,
            'port': self.WEB_PORT,
            'secret_key': self.WEB_SECRET_KEY,
            'theme': self.WEB_THEME,
            'auth_enabled': self.ENABLE_WEB_AUTH,
            'username': self.WEB_USERNAME,
            'password': self.WEB_PASSWORD
        }
    
    def validate_config(self):
        """Validate configuration values"""
        errors = []
        
        # Validate camera settings
        if not isinstance(self.CAMERA_SOURCE, (int, str)):
            errors.append("CAMERA_SOURCE must be an integer or string")
        
        if not (0 <= self.DETECTION_CONFIDENCE <= 1):
            errors.append("DETECTION_CONFIDENCE must be between 0 and 1")
        
        if not (0 <= self.IOU_THRESHOLD <= 1):
            errors.append("IOU_THRESHOLD must be between 0 and 1")
        
        # Validate paths
        if not os.path.exists(os.path.dirname(self.DATABASE_PATH)):
            errors.append(f"Database directory does not exist: {os.path.dirname(self.DATABASE_PATH)}")
        
        # Validate web settings
        if not (1 <= self.WEB_PORT <= 65535):
            errors.append("WEB_PORT must be between 1 and 65535")
        
        # Validate email settings
        if self.EMAIL_ENABLED:
            if not self.EMAIL_USERNAME or not self.EMAIL_PASSWORD:
                errors.append("Email credentials are required when EMAIL_ENABLED is True")
        
        return errors
    
    def print_config_summary(self):
        """Print a summary of current configuration"""
        print("\n📋 Configuration Summary")
        print("=" * 50)
        print(f"Project: {self.PROJECT_NAME} v{self.VERSION}")
        print(f"Debug Mode: {self.DEBUG}")
        print(f"Camera Source: {self.CAMERA_SOURCE}")
        print(f"Camera Resolution: {self.CAMERA_WIDTH}x{self.CAMERA_HEIGHT}")
        print(f"Detection Model: {self.YOLO_MODEL_PATH}")
        print(f"Detection Confidence: {self.DETECTION_CONFIDENCE}")
        print(f"Web Interface: {self.WEB_HOST}:{self.WEB_PORT}")
        print(f"Database: {self.DATABASE_PATH}")
        print(f"Video Recording: {self.ENABLE_VIDEO_RECORDING}")
        print(f"Notifications: {self.ENABLE_NOTIFICATIONS}")
        print(f"Motion Detection: {self.ENABLE_MOTION_DETECTION}")
        print(f"Detection Zones: {self.ENABLE_DETECTION_ZONES}")
        print(f"Scheduled Monitoring: {self.ENABLE_SCHEDULE}")
        print(f"Data Retention: {self.DATA_RETENTION_DAYS} days")
        print("=" * 50)
    
    def get_environment_config(self):
        """Get configuration from environment variables"""
        env_mappings = {
            'SECURITY_CAMERA_SOURCE': 'CAMERA_SOURCE',
            'SECURITY_MODEL_PATH': 'YOLO_MODEL_PATH',
            'SECURITY_CONFIDENCE': 'DETECTION_CONFIDENCE',
            'SECURITY_WEB_HOST': 'WEB_HOST',
            'SECURITY_WEB_PORT': 'WEB_PORT',
            'SECURITY_DATABASE_PATH': 'DATABASE_PATH',
            'SECURITY_DEBUG': 'DEBUG',
            'SECURITY_SECRET_KEY': 'WEB_SECRET_KEY',
            'SECURITY_EMAIL_USERNAME': 'EMAIL_USERNAME',
            'SECURITY_EMAIL_PASSWORD': 'EMAIL_PASSWORD',
            'SECURITY_WEBHOOK_URL': 'WEBHOOK_URL'
        }
        
        for env_var, config_attr in env_mappings.items():
            if env_var in os.environ:
                value = os.environ[env_var]
                
                # Convert string values to appropriate types
                if config_attr in ['CAMERA_SOURCE', 'WEB_PORT', 'DATA_RETENTION_DAYS']:
                    try:
                        value = int(value)
                    except ValueError:
                        print(f"⚠️  Invalid integer value for {env_var}: {value}")
                        continue
                
                elif config_attr in ['DETECTION_CONFIDENCE', 'IOU_THRESHOLD']:
                    try:
                        value = float(value)
                    except ValueError:
                        print(f"⚠️  Invalid float value for {env_var}: {value}")
                        continue
                
                elif config_attr in ['DEBUG', 'ENABLE_NOTIFICATIONS', 'EMAIL_ENABLED']:
                    value = value.lower() in ['true', '1', 'yes', 'on']
                
                setattr(self, config_attr, value)
                print(f"✅ Environment variable {env_var} applied")

# Create a default configuration instance
config = Config()

# Example usage and configuration file creation
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Security System Configuration')
    parser.add_argument('--create-config', action='store_true',
                       help='Create a new configuration file')
    parser.add_argument('--validate', action='store_true',
                       help='Validate current configuration')
    parser.add_argument('--summary', action='store_true',
                       help='Print configuration summary')
    parser.add_argument('--config-file', default='config.json',
                       help='Configuration file path')
    
    args = parser.parse_args()
    
    # Initialize configuration
    config = Config(args.config_file)
    
    if args.create_config:
        config.save_config()
        print("✅ Configuration file created successfully!")
    
    if args.validate:
        errors = config.validate_config()
        if errors:
            print("❌ Configuration validation failed:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("✅ Configuration is valid!")
    
    if args.summary:
        config.print_config_summary()
    
    if not any([args.create_config, args.validate, args.summary]):
        print("Use --help for available options")
        config.print_config_summary()